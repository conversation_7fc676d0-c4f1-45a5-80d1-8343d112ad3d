import { create } from 'zustand';
import {
  CreateEventRegistration,
  EventRegistrationBillingAddress,
  EventRegistrationBillingMethod,
  EventRegistrationContact,
  EventRegistrationContactType,
  EventRegistrationForm,
  EventRegistrationFormFieldBehavior,
  EventRegistrationF<PERSON>FieldType,
  EventRegistrationRegisteredBy,
  EventRegistrationResponse,
  EventRegistrationType,
  Problem,
} from 'src/generated/api/dsv-public/model';

export type FormStep = (typeof FormStep)[keyof typeof FormStep];

export const FormStep = {
  EVENT_DETAILS: 'EVENT_DETAILS',
  PARTICIPANTS: 'PARTICIPANTS',
  EMERGENCY_CONTACT: 'EMERGENCY_CONTACT',
  REGISTRANT: 'REGISTRANT',
  BILLING: 'BILLING',
  CONFIRMATION: 'CONFIRMATION',
} as const;

export const FormStepOrder = [
  FormStep.EVENT_DETAILS,
  FormStep.PARTICIPANTS,
  FormStep.EMERGENCY_CONTACT,
  FormStep.REGISTRANT,
  FormStep.BILLING,
  FormStep.CONFIRMATION,
];

type InvalidField = {
  key: string;
  message?: string;
};

type FormProgressStore = {
  registrant: EventRegistrationRegisteredBy | undefined;
  registrationSelection: EventRegistrationType | undefined;
  registrationCode: string | undefined;
  registrationEntries: CreateEventRegistration[];
  contacts: EventRegistrationContact[];
  billingAddress: EventRegistrationBillingAddress | undefined;
  billingMethod: EventRegistrationBillingMethod;
  forceErrorMessages: boolean;

  setRegistrant: (registrant: EventRegistrationRegisteredBy) => void;
  setRegistrationSelection: (registrationSelection: EventRegistrationType) => void;
  setRegistrationCode: (registrationCode: string) => void;
  setRegistrationEntries: (registrationEntries: CreateEventRegistration[]) => void;
  addParticipant: (form: EventRegistrationForm) => void;
  removeParticipant: (index: number) => void;
  setContacts: (contacts: EventRegistrationContact[]) => void;
  setBillingAddress: (billingAddress: EventRegistrationBillingAddress) => void;
  setBillingMethod: (billingMethod: EventRegistrationBillingMethod) => void;
  setForceErrorMessages: (forceErrorMessages: boolean) => void;

  addContact: () => void;
  removeContact: (index: number) => void;

  invalidFields: InvalidField[];
  updateFieldValidation: (fieldKey: string, isValid: boolean, message?: string) => void;

  currentStep: FormStep;
  setCurrentStep: (step: FormStep) => void;

  submissionResult: EventRegistrationResponse | Problem | undefined;
  setSubmissionResult: (result: EventRegistrationResponse | Problem) => void;
};

export const useFormProgressStore = create<FormProgressStore>(set => ({
  registrant: undefined,
  registrationSelection: undefined,
  registrationCode: undefined,
  registrationEntries: [],
  contacts: [],
  billingAddress: undefined,
  billingMethod: EventRegistrationBillingMethod.EMAIL,
  forceErrorMessages: false,
  setRegistrant: (registrant: EventRegistrationRegisteredBy) => set({ registrant }),
  setRegistrationSelection: (registrationSelection: EventRegistrationType) => set({ registrationSelection }),
  setRegistrationCode: (registrationCode: string) => set({ registrationCode }),
  setRegistrationEntries: (registrationEntries: CreateEventRegistration[]) => set({ registrationEntries }),
  setContacts: (contacts: EventRegistrationContact[]) => set({ contacts }),
  setBillingAddress: (billingAddress: EventRegistrationBillingAddress) => set({ billingAddress }),
  setBillingMethod: (billingMethod: EventRegistrationBillingMethod) => set({ billingMethod }),
  setForceErrorMessages: (forceErrorMessages: boolean) => set({ forceErrorMessages }),

  addContact: () =>
    set(prev => ({
      contacts: prev.contacts.concat({
        type: EventRegistrationContactType.PARENT,
        firstName: '',
        lastName: '',
        mobile: '',
        email: '',
      }),
    })),
  removeContact: (index: number) => set(prev => ({ contacts: prev.contacts.filter((_, i) => i !== index) })),

  addParticipant: (form: EventRegistrationForm) =>
    set(prev => {
      if (prev.registrationEntries.length === 0) {
        return {
          registrationEntries: [
            {
              fields: form.fields.reduce((acc, f) => {
                if (
                  (f.type === EventRegistrationFormFieldType.CHECKBOX ||
                    f.type === EventRegistrationFormFieldType.MULTI_SELECT) &&
                  f.defaultValue !== undefined
                ) {
                  return { ...acc, [f.key]: Array.isArray(f.defaultValue) ? f.defaultValue : [f.defaultValue] };
                }
                return { ...acc, [f.key]: f.defaultValue };
              }, {}),
              options: [],
            },
          ],
        };
      }

      const firstParticipant = prev.registrationEntries[0];
      const newParticipantFields = form.fields.reduce((acc, field) => {
        const fieldKey = field.key;
        let fieldValue = field.defaultValue;

        if (
          field.behavior === EventRegistrationFormFieldBehavior.ALL_COPIED ||
          field.behavior === EventRegistrationFormFieldBehavior.FIRST_APPLIED_TO_OTHERS
        ) {
          // @ts-ignore
          fieldValue = firstParticipant.fields[fieldKey];
        } else if (
          (field.type === EventRegistrationFormFieldType.CHECKBOX ||
            field.type === EventRegistrationFormFieldType.MULTI_SELECT) &&
          fieldValue !== undefined
        ) {
          // @ts-ignore
          fieldValue = Array.isArray(fieldValue) ? fieldValue : [fieldValue];
        }

        return { ...acc, [fieldKey]: fieldValue };
      }, {});

      const newParticipantOptions: number[] = [];
      form.pricingOptionGroups?.forEach(group => {
        if (
          group.behavior === EventRegistrationFormFieldBehavior.ALL_COPIED ||
          group.behavior === EventRegistrationFormFieldBehavior.FIRST_APPLIED_TO_OTHERS
        ) {
          const firstParticipantOptions = firstParticipant.options || [];
          const groupOptionIds = group.options.map(option => Number(option.optionId));
          const selectedOptions = firstParticipantOptions.filter(optionId => groupOptionIds.includes(optionId));
          newParticipantOptions.push(...selectedOptions);
        }
      });

      return {
        registrationEntries: prev.registrationEntries.concat({
          fields: newParticipantFields,
          options: newParticipantOptions,
        }),
      };
    }),

  removeParticipant: (index: number) =>
    set(prev => {
      if (prev.registrationEntries.length <= 1) return prev;

      return {
        registrationEntries: prev.registrationEntries.filter((_, i) => i !== index),
        invalidFields: prev.invalidFields.filter(invF => invF.key.startsWith(`${index}-`)),
      };
    }),

  invalidFields: [],
  updateFieldValidation: (fieldKey: string, isValid: boolean, message?: string) =>
    set(prev => {
      if (isValid) {
        return {
          invalidFields: prev.invalidFields.filter(invF => invF.key !== fieldKey),
        };
      }

      const invalidFieldsWithoutCur = prev.invalidFields.filter(invF => invF.key !== fieldKey);
      return {
        invalidFields: invalidFieldsWithoutCur.concat({ key: fieldKey, message }),
      };
    }),

  currentStep: FormStep.EVENT_DETAILS,
  setCurrentStep: (step: FormStep) => set({ currentStep: step }),

  submissionResult: undefined,
  setSubmissionResult: (result: EventRegistrationResponse | Problem) => set({ submissionResult: result }),
}));
